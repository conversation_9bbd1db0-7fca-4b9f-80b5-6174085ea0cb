'use client';
import Image from 'next/image';
import React, { useMemo } from 'react';

const CanvasPreview = ({ skin, contentData = {} }) => {
  const {
    canvasItems = [],
    canvasWidth = 0,
    canvasHeight = 0,
    canvasBackground = '#fff',
  } = skin || {};

  // Memoize merged items to prevent unnecessary renders
  const mergedItems = useMemo(
    () =>
      canvasItems.map((item) => {
        // Override content only for fixed keys if contentData provided
        if (
          ['subject', 'date', 'body'].includes(item.id) &&
          contentData[item.id]
        ) {
          return {
            ...item,
            content: contentData[item.id],
          };
        }
        return item;
      }),
    [canvasItems, contentData]
  );

  return (
    <div
      style={{
        position: 'relative',
        width: canvasWidth,
        height: canvasHeight,
        background: canvasBackground,
        overflow: 'hidden',
        boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
        margin: '0 auto',
        border: '1px solid #e5e7eb',
      }}
    >
      {mergedItems.map((item) => (
        <div
          key={item.id}
          style={{
            position: 'absolute',
            left: item.styles?.x,
            top: item.styles?.y,
            width: item.styles?.width,
            height: item.styles?.height,
            zIndex: item.zIndex,
          }}
        >
          {item.type === 'image' ? (
            <div
              style={{
                position: 'relative',
                width: '100%',
                height: '100%',
              }}
            >
              {item.image ? (
                <Image
                  src={item.image}
                  alt="Skin element"
                  fill
                  sizes="(max-width: 200px) 100vw"
                  style={{ objectFit: 'contain' }}
                  onError={(e) => {
                    console.error('Image failed to load:', item.image);
                    e.target.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400 text-xs">
                  Image not available
                </div>
              )}
            </div>
          ) : (
            <div
              style={{
                color: item.styles?.color || '#000000',
                fontFamily: item.styles?.fontFamily || 'Roboto',
                fontSize: item.styles?.fontSize || '1rem',
                textAlign: item.styles?.textAlign || 'left',
                display: 'block',
                padding: '8px',
                ...(item.textType === 'body' && {
                  maxHeight: '100%',
                  overflowY: 'auto',
                  minHeight: '200px',
                }),
              }}
            >
              {item.content}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default CanvasPreview;
