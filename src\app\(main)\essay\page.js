'use client';

import { useEffect, useCallback, useState } from 'react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { useSearchParams } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';

import api from '@/lib/api';

import {
  setSubject,
  setMessage,
  setSelectedSkin,
  setIsSaving,
  setIsLoading,
  setTodayEntry,
  setIsSkinModalOpen,
  setLayoutBackground,
  selectDiarySubject,
  selectDiaryMessage,
  selectSelectedSkin,
  selectIsSaving,
  selectIsLoading,
  selectTodayEntry,
  selectIsSkinModalOpen,
  selectLayoutBackground,
} from '@/store/features/diarySlice';
import SelectSkinModal from '../diary/_component/SelectSkinModal';
import StageSelector from '../diary/_component/StageSelector';
import DiaryForm from '../diary/_component/DiaryForm';
import StudentDiaryCanvas from '../diary/StudentDiaryCanvas';
import MessageModal from '../diary/_component/modalContents/MessageModal';
import useDataFetch from '@/hooks/useDataFetch';
import {
  addImageToCanvas,
  changeBackground,
  resetCanvas,
  addTextItem,
  setSelectedId,
} from '@/store/features/canvasSlice';



const handleSkinChange = async (
  newSkin,
  dispatch,
  setSelectedSkin,
  setSubject,
  setMessage
) => {
  if (!newSkin.templateContent) {
    toast.error('This skin has no valid template content');
    return;
  }

  try {
    // 1. First reset everything
    dispatch(resetCanvas());
    setSubject('');
    setMessage('');

    // 2. Then set the new skin
    setSelectedSkin(newSkin);

    // 3. Parse and apply template
    const templateData = JSON.parse(newSkin.templateContent);

    // 4. Set background if exists
    if (templateData.background) {
      dispatch(changeBackground(templateData.background));
    }

    // 5. Process items with a small delay
    setTimeout(() => {
      if (templateData.items?.length) {
        templateData.items.forEach((item) => {
          if (item.type === 'text') {
            let content = item.content;
            let itemId = item.id;

            // Handle special fields
            if (item.id === 'subject' || item.id.startsWith('subject')) {
              itemId = 'subject';
              content = item.content;
              setSubject(content); // Update subject state
            } else if (item.id === 'body' || item.id.startsWith('body')) {
              itemId = 'body';
              content = item.content;
              setMessage(content); // Update message state
            } else if (item.id === 'date' || item.id.startsWith('date')) {
              itemId = 'date';
              content = format(new Date(), item.dateFormat || 'dd MMM yyyy');
            }

            // Add text item with full styles
            dispatch({
              type: 'canvas/addTextItem',
              payload: {
                ...item,
                id: itemId,
                content,
                styles: {
                  ...item.styles,
                  width: item.styles?.width || 300,
                  height: item.styles?.height || 40,
                  x: item.styles?.x || 50,
                  y: item.styles?.y || 20,
                },
              },
            });
          } else if (item.type === 'image') {
            dispatch(
              addImageToCanvas({
                id: `${item.id}-${Date.now()}`,
                imageSrc: item.image,
                styles: item.styles,
                zIndex: item.zIndex || 1,
              })
            );
          }
        });
      }

      // Force refresh
      dispatch(setPreviewMode(false));
      setTimeout(() => dispatch(setPreviewMode(true)), 50);
    }, 50);

    toast.success(`Skin "${newSkin.name}" applied successfully`);
  } catch (error) {
    console.error('Error applying skin template:', error);
    toast.error('Failed to apply skin template');
  }
};


const applyTemplate = (templateData, essayData, dispatch) => {
  if (templateData.background) {
    dispatch(changeBackground(templateData.background));
  }

  if (templateData.items?.length) {
    templateData.items.forEach((item) => {
      if (item.type === 'text') {
        let contentForCanvas = item.content; // Start with template's default content
        let itemId = item.id;

        // Determine content for the CANVAS ITEM
        if (item.id === 'subject' || item.id.startsWith('subject')) {
          itemId = 'subject';
          // Use essay data's title for the canvas if it exists, otherwise use template content
          contentForCanvas = essayData?.title || item.content;
        } else if (item.id === 'body' || item.id.startsWith('body')) {
          itemId = 'body';
          // Use essay data's content for the canvas if it exists, otherwise use template content
          contentForCanvas = essayData?.content || item.content;
        } else if (item.id === 'date' || item.id.startsWith('date')) {
          itemId = 'date';
          contentForCanvas = format(
            new Date(),
            item.dateFormat || 'dd MMM yyyy'
          );
        }

        // Use the proper action creator instead of raw action object
        dispatch(addTextItem({
          ...item,
          id: itemId,
          content: contentForCanvas, // Use the determined content for the canvas item
        }));
      } else if (item.type === 'image') {
        dispatch(
          addImageToCanvas({
            id: `${item.id}-${Date.now()}`,
            imageSrc: item.image,
            styles: item.styles,
            zIndex: item.zIndex || 1,
          })
        );
      }
    });
  }
};

export default function WriteEssay() {
  const dispatch = useDispatch();
  const essayId = useSearchParams().get('essayId');

  const today = format(new Date(), 'dd MMM yyyy');
  const [selectedStageTemplateId, setSelectedStageTemplateId] = useState(null);
  const [selectedStage, setSelectedStage] = useState(null);
  const [wordCount] = useState(0);

  const subject = useSelector(selectDiarySubject);
  const message = useSelector(selectDiaryMessage);
  const selectedSkin = useSelector(selectSelectedSkin);
  const isSaving = useSelector(selectIsSaving);
  // const isLoading = useSelector(selectIsLoading);
  const todayEntry = useSelector(selectTodayEntry);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);
  const layoutBackground = useSelector(selectLayoutBackground);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);


  const { data: skinInfo, isLoading } = useDataFetch({
    queryKey: ['essay-skins'],
    endPoint: `/student-essay/skins/${essayId}`,
  });

  useEffect(() => {
    // Handle the API response structure: data.moduleDefaultSkin or data.taskSpecificSkin
    const skinData = skinInfo?.taskSpecificSkin || skinInfo?.moduleDefaultSkin;

    if (skinData) {
      console.log(skinData, 'skin data');
      dispatch(setTodayEntry(skinData));

      // Initialize form fields - these might be empty for new essays
      dispatch(setSubject(skinData.title || ''));
      dispatch(setMessage(skinData.content || ''));

      // Set the selected stage template ID from the skin data if available
      if (skinData.settings?.settingsTemplateId) {
        setSelectedStageTemplateId(skinData.settings.settingsTemplateId);
      }

      // Apply the skin template if available
      if (skinData.skin) {
        dispatch(setSelectedSkin(skinData.skin));
        dispatch(resetCanvas());

        // Set background from skin data if available
        if (skinData.backgroundColor) {
          dispatch(changeBackground(skinData.backgroundColor));
        }

        // Parse and apply the template content
        if (skinData.skin.templateContent) {
          try {
            const templateData = JSON.parse(skinData.skin.templateContent);
            applyTemplate(templateData, skinData, dispatch);

            // Clear selection after applying template
            dispatch(setSelectedId(null));
          } catch (error) {
            console.error('Error parsing template content:', error);
          }
        }
      }
    }
  }, [skinInfo, dispatch]);

  const handleStageChange = useCallback((stage) => {
    setSelectedStageTemplateId(stage.id);
    setSelectedStage(stage);
  }, []);

  // Handle save
  const handleSave = useCallback(async () => {
    if (!subject.trim() && !message.trim()) {
      toast.warning('Please enter either subject or content');
      return;
    }

    dispatch(setIsSaving(true));

    try {
      const payload = {
        title: subject,
        content: message,
        taskId: essayId || null,
      };

      let apiCall;

      // Check if entry exists
      if (todayEntry?.id) {
        // Check if status is "new" - if so, use the submit endpoint
        apiCall = api.post(`/student-essay/submit/task`, payload);
      } else {
        // If no entry exists yet, create a new one
        apiCall = api.post('/diary/entries', {
          ...payload,
          entryDate: format(new Date(), 'yyyy-MM-dd'),
        });
      }

      const response = await apiCall;

      if (response.success) {
        dispatch(setTodayEntry(response.data));

        // Show appropriate success message based on the action performed
        if (todayEntry?.status === 'new' && todayEntry?.id) {
          toast.success('Diary submitted successfully!');
        } else {
          toast.success('Diary saved successfully!');
        }
      }
    } catch (error) {
      console.error('Error saving diary entry:', error);
      toast.error('Failed to save diary entry. Please try again.');
    } finally {
      dispatch(setIsSaving(false));
    }
  }, [
    subject,
    message,
    selectedSkin,
    layoutBackground,
    todayEntry,
    dispatch,
    selectedStageTemplateId,
  ]);

  return (
    <>
      <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8 flex items-center gap-2">
        <div className="w-full min-h-[500px] bg-pink-100 p-2">
          {isLoading ? (
            <div className="flex items-center justify-center h-[500px]">
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mb-4"></div>
                <h2 className="text-xl font-semibold">Loading your diary...</h2>
              </div>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 items-center relative">
                <div className="bg-white h-full  p-2 overflow-hidden shadow-xl">
                  {/* Stage Selector with the selected stage template ID */}
                  <div className="mb-4">
                    <StageSelector
                      onStageChange={handleStageChange}
                      selectedTemplateId={selectedStageTemplateId}
                    />
                  </div>
                  <div className="w-full h-[500px] flex items-center justify-center overflow-hidden">
                    <div
                      className="canvas-container-wrapper"
                      style={{ width: '100%', height: '100%' }}
                    >
                      <StudentDiaryCanvas data={todayEntry}  />
                    </div>
                  </div>
                  <div className="flex justify-center ">
                    <button
                      onClick={handleSave}
                      disabled={isSaving}
                      className={`  text-black font-medium py-2 px-8  text-center rounded-full whitespace-nowrap
                            border-2 border-yellow-100
                            shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                            transition-all duration-300
                            bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                            relative pr-8
                            ring-2 ring-[#A36105] ${
                              isSaving
                                ? 'bg-gray-300 cursor-not-allowed'
                                : 'bg-yellow-400 hover:bg-yellow-300'
                            }`}
                    >
                      {isSaving ? 'Saving...' : todayEntry ? 'Submit' : 'Save'}
                    </button>
                    {/* )} */}
                  </div>
                </div>

                <div className="bg-white h-full p-2 overflow-hidden shadow-xl flex flex-col gap-4">
                  <DiaryForm
                    today={today}
                    todayEntry={todayEntry}
                    subject={subject}
                    message={message}
                    wordCount={wordCount}
                    selectedStage={selectedStage}
                  />
                </div>
              </div>
            </>
          )}

          <SelectSkinModal
            isOpen={isSkinModalOpen}
            onClose={() => dispatch(setIsSkinModalOpen(false))}
            onApply={(skin) => {
              dispatch(setSelectedSkin(skin));
              dispatch(resetCanvas());

              if (skin.templateContent) {
                try {
                  const templateData = JSON.parse(skin.templateContent);
                  applyTemplate(templateData, todayEntry, dispatch);
                  dispatch(setSelectedId(null));
                } catch (error) {
                  console.error('Error parsing template content:', error);
                }
              }
            }}
            currentSkinId={selectedSkin?.id}
          />
          <MessageModal
            isOpen={isMessageModalOpen}
            onClose={() => setIsMessageModalOpen(false)}
          />
        </div>
      </div>
    </>
  );
}
